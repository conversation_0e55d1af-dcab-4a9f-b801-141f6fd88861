# Cómo Habilitar Autoscaling para Servicios Adicionales

## Estado Actual

Actualmente solo `enterprise-webservice-api` tiene autoscaling habilitado. Los demás servicios están configurados con `enable_autoscaling = false`.

## Habilitar Autoscaling para un Servicio

### Paso 1: Modificar la configuración en enterprise_service.tf

Para habilitar autoscaling en `enterprise-account-api`, cambiar:

```hcl
"enterprise-account-api" = {
  image            = "ubuntu"
  cpu              = "256"
  memory           = "512"
  ports            = 8080
  health_check_url = "/account-cl/health"
  enable_efs       = false
  efs_mount_path   = "/mnt/shared"
  enable_autoscaling = true  # Cambiar de false a true
},
```

### Paso 2: Aplicar los cambios

```bash
cd terraform
terraform plan
terraform apply
```

### Paso 3: Verificar que el autoscaling fue habilitado

```bash
# Verificar autoscaling target
aws application-autoscaling describe-scalable-targets \
  --service-namespace ecs \
  --resource-ids "service/your-cluster-name/enterprise-account-api"

# Verificar políticas de autoscaling
aws application-autoscaling describe-scaling-policies \
  --service-namespace ecs \
  --resource-id "service/your-cluster-name/enterprise-account-api"
```

## Habilitar Autoscaling para Múltiples Servicios

Para habilitar autoscaling en varios servicios a la vez:

```hcl
locals {
  enterprise_apis = {
    "enterprise-account-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/account-cl/health"
      enable_efs       = false
      efs_mount_path   = "/mnt/shared"
      enable_autoscaling = true  # ✅ Habilitado
    },
    "enterprise-general-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/general-cl/health"
      enable_efs       = false
      enable_autoscaling = true  # ✅ Habilitado
    },
    "enterprise-blacklist-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/blacklist-cl/health"
      enable_efs       = false
      enable_autoscaling = false  # ❌ Deshabilitado
    },
    "enterprise-webservice-api" = {
      image            = "ubuntu"
      cpu              = "256"
      memory           = "512"
      ports            = 8080
      health_check_url = "/ws-enterprise-cl/health"
      enable_efs       = false
      enable_autoscaling = true  # ✅ Ya habilitado
    },
    # ... resto de servicios
  }
}
```

## Deshabilitar Autoscaling

Para deshabilitar autoscaling en un servicio:

```hcl
"enterprise-webservice-api" = {
  image            = "ubuntu"
  cpu              = "256"
  memory           = "512"
  ports            = 8080
  health_check_url = "/ws-enterprise-cl/health"
  enable_efs       = false
  enable_autoscaling = false  # Cambiar de true a false
},
```

**⚠️ Importante**: Al deshabilitar autoscaling, el servicio volverá a tener capacidad fija de 1 instancia.

## Configuración Personalizada por Servicio

Si necesitas configuraciones de autoscaling diferentes por servicio, puedes extender la configuración:

```hcl
"enterprise-report-api" = {
  image            = "ubuntu"
  cpu              = "512"
  memory           = "1024"
  ports            = 8080
  health_check_url = "/report-cl/health"
  enable_efs       = true
  efs_mount_path   = "/reports"
  enable_autoscaling = true
  # Configuraciones personalizadas (futuro)
  # custom_min_capacity = 2
  # custom_max_capacity = 20
  # custom_cpu_target = 60
},
```

## Monitoreo después de Habilitar

Después de habilitar autoscaling en un servicio:

1. **Verificar métricas en CloudWatch**
2. **Monitorear eventos de scaling**
3. **Probar con carga de trabajo real**

```bash
# Monitorear número de tareas en tiempo real
watch -n 30 'aws ecs describe-services --cluster your-cluster --services enterprise-account-api --query "services[0].{Desired:desiredCount,Running:runningCount,Pending:pendingCount}"'
```

## Consideraciones

- **Costo**: Más instancias = mayor costo
- **Performance**: El autoscaling mejora la disponibilidad bajo carga
- **Tiempo**: El scaling puede tomar 1-3 minutos en activarse
- **Métricas**: Asegúrate de que el servicio reciba tráfico para generar métricas

## Rollback

Si necesitas revertir los cambios:

```bash
# Volver a la configuración anterior
git checkout HEAD~1 terraform/enterprise_service.tf
terraform plan
terraform apply
```
