# Pruebas de Autoscaling para ECS Services

Este documento describe cómo probar el autoscaling implementado en los servicios ECS.

## Configuración Implementada

### Políticas de Autoscaling

Cada servicio tiene tres políticas de autoscaling configuradas:

1. **CPU Autoscaling**
   - Métrica: `ECSServiceAverageCPUUtilization`
   - Target: 70% (configurable por ambiente)
   - Scale-out cooldown: 60 segundos
   - Scale-in cooldown: 300 segundos

2. **Memory Autoscaling**
   - Métrica: `ECSServiceAverageMemoryUtilization`
   - Target: 70% (configurable por ambiente)
   - Scale-out cooldown: 60 segundos
   - Scale-in cooldown: 300 segundos

3. **Request Count Autoscaling**
   - Métrica: `ALBRequestCountPerTarget`
   - Target: 1000 requests/target (dev), 1500 (stg), 2000 (prd)
   - Scale-out cooldown: 60 segundos
   - Scale-in cooldown: 300 segundos

### Capacidades por Ambiente

| Ambiente | Min Capacity | Max Capacity |
|----------|--------------|--------------|
| dev      | 1            | 10           |
| stg      | 5            | 30           |
| prd      | 5            | 30           |

## Comandos para Verificar el Autoscaling

### 1. Verificar que los recursos de autoscaling fueron creados

```bash
# Listar autoscaling targets
aws application-autoscaling describe-scalable-targets \
  --service-namespace ecs \
  --resource-ids "service/your-cluster-name/enterprise-account-api"

# Listar políticas de autoscaling
aws application-autoscaling describe-scaling-policies \
  --service-namespace ecs \
  --resource-id "service/your-cluster-name/enterprise-account-api"
```

### 2. Monitorear el estado actual del servicio

```bash
# Ver el número actual de tareas
aws ecs describe-services \
  --cluster your-cluster-name \
  --services enterprise-account-api \
  --query 'services[0].{DesiredCount:desiredCount,RunningCount:runningCount,PendingCount:pendingCount}'
```

### 3. Verificar métricas en CloudWatch

```bash
# CPU Utilization
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name CPUUtilization \
  --dimensions Name=ServiceName,Value=enterprise-account-api Name=ClusterName,Value=your-cluster-name \
  --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 \
  --statistics Average

# Memory Utilization
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name MemoryUtilization \
  --dimensions Name=ServiceName,Value=enterprise-account-api Name=ClusterName,Value=your-cluster-name \
  --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 \
  --statistics Average
```

## Pruebas de Carga para Activar Autoscaling

### 1. Prueba de CPU/Memory Load

Puedes usar herramientas como `stress` dentro del contenedor o generar carga desde el exterior:

```bash
# Ejemplo usando Apache Bench para generar carga
ab -n 10000 -c 100 https://enterprise-account-api.your-domain.com/account-cl/health
```

### 2. Prueba de Request Count

```bash
# Generar muchas peticiones concurrentes
for i in {1..10}; do
  ab -n 1000 -c 50 https://enterprise-account-api.your-domain.com/account-cl/health &
done
```

### 3. Monitorear el Scaling en Tiempo Real

```bash
# Script para monitorear el número de tareas cada 30 segundos
while true; do
  echo "$(date): $(aws ecs describe-services --cluster your-cluster-name --services enterprise-account-api --query 'services[0].{Desired:desiredCount,Running:runningCount}' --output text)"
  sleep 30
done
```

## Verificar Logs de Autoscaling

Los eventos de autoscaling se pueden ver en:

1. **CloudWatch Logs**: Buscar logs de Application Auto Scaling
2. **ECS Console**: En la pestaña "Events" del servicio
3. **CloudTrail**: Para eventos de API calls de autoscaling

```bash
# Ver eventos recientes del servicio ECS
aws ecs describe-services \
  --cluster your-cluster-name \
  --services enterprise-account-api \
  --query 'services[0].events[0:10]'
```

## Troubleshooting

### Problemas Comunes

1. **El autoscaling no se activa**
   - Verificar que las métricas están siendo reportadas a CloudWatch
   - Confirmar que los targets y políticas están correctamente configurados
   - Revisar que el servicio tiene suficiente capacidad en el cluster

2. **Scale-in muy lento**
   - Es normal, el scale-in tiene un cooldown de 300 segundos por defecto
   - AWS es conservador para evitar "flapping"

3. **Métricas no disponibles**
   - Las métricas de ECS pueden tardar hasta 5 minutos en aparecer
   - Verificar que el servicio está recibiendo tráfico

### Comandos de Diagnóstico

```bash
# Verificar capacidad del cluster
aws ecs describe-clusters --clusters your-cluster-name

# Ver detalles de las tareas
aws ecs list-tasks --cluster your-cluster-name --service-name enterprise-account-api

# Describir una tarea específica
aws ecs describe-tasks --cluster your-cluster-name --tasks task-arn
```
