locals {
  ecr_repositories_all = {
    dev = ["enterprise-account-api" , "enterprise-general-api",
           "enterprise-blacklist-api","enterprise-webservice-api",
           "enterprise-report-api","enterprise-maintenance-api",
           "common-shorturl-api","common-mailer-api"],
    stg = []
    prd = ["enterprise-account-api" , "enterprise-general-api",
           "enterprise-blacklist-api","enterprise-webservice-api",
           "enterprise-report-api","enterprise-maintenance-api",
           "common-shorturl-api","common-mailer-api"]
  }
  ecr_repositories = lookup(local.ecr_repositories_all, local.env, [])
}
module "ecr" {
  for_each        = toset(local.ecr_repositories)
  source          = "./modules/ecr-repository"
  repository_name = each.key
}