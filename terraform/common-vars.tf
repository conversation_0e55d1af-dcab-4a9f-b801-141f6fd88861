#######################################################
# Expected env vars (TF_VAR_)

variable "app_name" { default = "bb2-aws-infra" }
variable "commit" { default = "Not deployed by pipeline" }
variable "repository" { default = "Not deployed by pipeline" }

#######################################################
# Local calculations:

locals {

  app_name   = var.app_name
  commit     = var.commit
  repository = "https://bitbucket.org/${var.repository}"

  env          = terraform.workspace
  cluster_name = local.infrastructure.ecs_cluster_name

}

output "env" { value = local.env }
#output "commit" { value = local.commit }
output "repository" { value = local.repository }

#######################################################
# Tags:

locals {

  # Provider Tags (to add tags even if we forget to put them on resources):
  # Must use them on provider.
  default_tags = {
    Name       = local.app_name
    terraform  = "true"
    team       = local.team
    app_name   = local.app_name
    repository = local.repository
    env        = local.env
  }


  # Merge with provider data if needed:
  #tags = merge(local.default_tags, local.provider_data)
  tags = local.aws_provider_data


}

  locals {
    retention_in_days_all = {
      dev=7
      prd=14
    }
    retention_in_days = lookup(local.retention_in_days_all, local.env, 7)
  }
