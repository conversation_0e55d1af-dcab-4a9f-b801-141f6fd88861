
output "task_definition_arn" {
  value = aws_ecs_task_definition.td.arn
}

output "efs_file_system_id" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].id : null
  description = "EFS file system ID (if EFS is enabled)"
}

output "efs_file_system_arn" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].arn : null
  description = "EFS file system ARN (if EFS is enabled)"
}

output "efs_dns_name" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].dns_name : null
  description = "EFS DNS name (if EFS is enabled)"
}

# Autoscaling outputs
output "autoscaling_target_resource_id" {
  value       = var.enable_autoscaling ? aws_appautoscaling_target.ecs_target[0].resource_id : null
  description = "Autoscaling target resource ID"
}

output "cpu_policy_arn" {
  value       = var.enable_autoscaling ? aws_appautoscaling_policy.cpu_policy[0].arn : null
  description = "CPU autoscaling policy ARN"
}

output "memory_policy_arn" {
  value       = var.enable_autoscaling ? aws_appautoscaling_policy.memory_policy[0].arn : null
  description = "Memory autoscaling policy ARN"
}

output "requests_policy_arn" {
  value       = var.enable_autoscaling ? aws_appautoscaling_policy.requests_policy[0].arn : null
  description = "Requests autoscaling policy ARN"
}
