
output "task_definition_arn" {
  value = aws_ecs_task_definition.td.arn
}

output "efs_file_system_id" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].id : null
  description = "EFS file system ID (if EFS is enabled)"
}

output "efs_file_system_arn" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].arn : null
  description = "EFS file system ARN (if EFS is enabled)"
}

output "efs_dns_name" {
  value       = var.enable_efs ? aws_efs_file_system.efs[0].dns_name : null
  description = "EFS DNS name (if EFS is enabled)"
}
