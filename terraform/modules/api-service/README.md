
# API Service Module (Extended)

Este módulo incluye TODO lo necesario para desplegar una API en ECS con Fargate, incluyendo:

- Roles IAM (execution/task roles + permisos logs + permisos EFS opcionales)
- Security Group con puertos restringidos
- CloudWatch log group
- ECS task definition y ECS service
- ALB Target Group y Listener Rule
- DNS específico en Route53
- EFS (Elastic File System) opcional con mount targets y security groups

## Uso

### Ejemplo básico (sin EFS)

```hcl
module "api_service" {
  source = "./modules/api-service"
  name   = "account-api"
  image  = "ubuntu"
  cpu    = "256"
  memory = "512"
  application_port = 8080
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30
}
```

### Ejemplo con EFS habilitado

```hcl
module "api_service_with_efs" {
  source = "./modules/api-service"
  name   = "report-api"
  image  = "ubuntu"
  cpu    = "512"
  memory = "1024"
  application_port = 8080
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30

  # EFS Configuration
  enable_efs = true
  efs_mount_path = "/mnt/shared"
  efs_performance_mode = "generalPurpose"
  efs_throughput_mode = "bursting"
}
```

## Variables de EFS

- `enable_efs`: (bool, default: false) Habilita la creación de EFS
- `efs_mount_path`: (string, default: "/mnt/efs") Ruta donde se montará EFS en el contenedor
- `efs_performance_mode`: (string, default: "generalPurpose") Modo de rendimiento de EFS
- `efs_throughput_mode`: (string, default: "bursting") Modo de throughput de EFS
