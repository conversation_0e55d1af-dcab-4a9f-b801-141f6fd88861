
# API Service Module (Extended)

Este módulo incluye TODO lo necesario para desplegar una API en ECS con Fargate, incluyendo:

- Roles IAM (execution/task roles + permisos logs + permisos EFS opcionales)
- Security Group con puertos restringidos
- CloudWatch log group
- ECS task definition y ECS service
- ALB Target Group y Listener Rule
- DNS específico en Route53
- EFS (Elastic File System) opcional con mount targets y security groups
- **Autoscaling automático** con políticas de CPU, memoria y peticiones (requests)

## Uso

### Ejemplo básico (sin EFS)

```hcl
module "api_service" {
  source = "./modules/api-service"
  name   = "account-api"
  image  = "ubuntu"
  cpu    = "256"
  memory = "512"
  application_port = 8080
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30
}
```

### Ejemplo con EFS y Autoscaling habilitado

```hcl
module "api_service_with_efs_and_autoscaling" {
  source = "./modules/api-service"
  name   = "report-api"
  image  = "ubuntu"
  cpu    = "512"
  memory = "1024"
  application_port = 8080
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30

  # EFS Configuration
  enable_efs = true
  efs_mount_path = "/mnt/shared"
  efs_performance_mode = "generalPurpose"
  efs_throughput_mode = "bursting"

  # Autoscaling Configuration (opcional)
  enable_autoscaling = true
  min_capacity = 1
  max_capacity = 10
  cpu_target_value = 70
  memory_target_value = 70
  requests_target_value = 1000
  scale_in_cooldown = 300
  scale_out_cooldown = 60
}
```

### Ejemplo sin autoscaling (configuración mínima)

```hcl
module "api_service_simple" {
  source = "./modules/api-service"
  name   = "simple-api"
  image  = "ubuntu"
  cpu    = "256"
  memory = "512"
  application_port = 8080
  health_check_url = "/health"
  vpc_id  = "vpc-..."
  subnet_ids = ["subnet-...", "subnet-..."]
  private_cidr_block = "10.0.0.0/16"
  cluster_name = "my-cluster"
  alb_listener_arn = "arn:aws:elasticloadbalancing:..."
  envzone_name = "example.com"
  envzone_zone_id = "Z123456ABCDEFG"
  alb_dns_name = "alb.example.com"
  log_retention_days = 30

  # Autoscaling deshabilitado por defecto
  enable_autoscaling = false
}
```

## Variables de EFS

- `enable_efs`: (bool, default: false) Habilita la creación de EFS
- `efs_mount_path`: (string, default: "/mnt/efs") Ruta donde se montará EFS en el contenedor
- `efs_performance_mode`: (string, default: "generalPurpose") Modo de rendimiento de EFS
- `efs_throughput_mode`: (string, default: "bursting") Modo de throughput de EFS

## Variables de Autoscaling

- `enable_autoscaling`: (bool, default: true) Habilita el autoscaling automático
- `min_capacity`: (number, default: 1) Número mínimo de tareas
- `max_capacity`: (number, default: 10) Número máximo de tareas
- `cpu_target_value`: (number, default: 70) Porcentaje objetivo de CPU para autoscaling
- `memory_target_value`: (number, default: 70) Porcentaje objetivo de memoria para autoscaling
- `requests_target_value`: (number, default: 1000) Número objetivo de peticiones por target para autoscaling
- `scale_in_cooldown`: (number, default: 300) Tiempo en segundos antes de poder hacer scale-in después de una actividad de scaling
- `scale_out_cooldown`: (number, default: 60) Tiempo en segundos antes de poder hacer scale-out después de una actividad de scaling

## Comportamiento del Autoscaling

El módulo configura tres políticas de autoscaling que funcionan de manera independiente:

### 1. Política de CPU
- **Métrica**: `ECSServiceAverageCPUUtilization`
- **Comportamiento**: Si el CPU promedio supera el `cpu_target_value` (default: 70%), se agregan nuevas instancias después de `scale_out_cooldown` segundos (default: 60s)
- **Scale-in**: Si el CPU se mantiene por debajo del target por más de `scale_in_cooldown` segundos (default: 300s), se eliminan instancias

### 2. Política de Memoria
- **Métrica**: `ECSServiceAverageMemoryUtilization`
- **Comportamiento**: Similar a CPU pero basado en utilización de memoria

### 3. Política de Peticiones (Requests)
- **Métrica**: `ALBRequestCountPerTarget`
- **Comportamiento**: Si el número de peticiones por target supera `requests_target_value` (default: 1000), se agregan nuevas instancias
- **Ventaja**: Permite escalar proactivamente basado en la carga de trabajo real

## Notas Importantes

- El `desired_count` del ECS service se establece en 1 y está configurado para ser ignorado por Terraform (`ignore_changes`)
- El autoscaling toma control del número de instancias después del despliegue inicial
- Las tres políticas funcionan simultáneamente, y AWS ECS elegirá la que requiera más instancias en cada momento
- Los cooldowns previenen el "flapping" (escalado excesivo hacia arriba y abajo)
