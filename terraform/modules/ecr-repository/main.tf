data "aws_ecr_lifecycle_policy_document" "policy_document" {
  rule {
    action {
      type = "expire"
    }
    priority = 1
    selection {
      tag_status       = "tagged"
      count_number     = 100
      count_type       = "imageCountMoreThan"
      tag_pattern_list = ["*"]
    }
  }
}

resource "aws_ecr_repository" "repo" {
  name = var.repository_name
}

resource "aws_ecr_lifecycle_policy" "policy" {
  policy     = data.aws_ecr_lifecycle_policy_document.policy_document.json
  repository = aws_ecr_repository.repo.name
}
