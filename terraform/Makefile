.SILENT:

# Check if env was defined (like: env=stg)
ifndef env
$(error env is not set)
endif



.PHONY: terraform-init
terraform-init:
	echo "===================================================================================="
	echo "Terraform init:"
	terraform -v 2> /dev/null
	terraform init
	echo
	echo "===================================================================================="
	echo "Terraform workspace and code validation for ${env}:"
	terraform workspace new ${env} || true
	terraform workspace select ${env}
	terraform validate
	echo "Let's get it on !!!"
	echo

.PHONY: terraform-plan
terraform-plan: terraform-init
	echo "===================================================================================="
	echo "Terraform plan (${env}):"
	terraform plan -no-color -compact-warnings -out plan-${env}.out | tee plan-${env}.txt
	terraform show -json plan-${env}.out > plan-${env}.json
	echo ${env} > plan-env

.PHONY: terraform-apply
terraform-apply: terraform-init
	echo "===================================================================================="
	echo "Terraform apply (${env}):"
	#terraform apply -compact-warnings -auto-approve plan-${env}.out
	terraform apply -compact-warnings -auto-approve
	echo
	echo "===================================================================================="
	echo "Terraform Output:"
	terraform output

.PHONY: terraform-plan-destroy
terraform-plan-destroy: terraform-init
	echo "===================================================================================="
	echo "Terraform plan destroy (${env}):"
	terraform plan -destroy -no-color -compact-warnings -out plan-destroy-${env}.out | tee plan-destroy-${env}.txt
	terraform show -json plan-destroy-${env}.out > plan-destroy-${env}.json
	echo ${env} > plan-env

.PHONY: terraform-destroy
terraform-destroy: terraform-init
	echo "===================================================================================="
	echo "Terraform destroy (${env}):"
	#terraform apply -compact-warnings -auto-approve plan-destroy-${env}.out
	terraform destroy -auto-approve -compact-warnings

.PHONY: terraform-output
terraform-output: terraform-init
	echo "===================================================================================="
	echo "Terraform output (${env}): ${arg}"
	terraform output -raw ${arg} | tee ../output
	echo "Saved output on file: output"

.PHONY: terraform-state-list
terraform-state-list: terraform-init
	echo "===================================================================================="
	echo "Terraform state list (${env}):"
	terraform state list

# In fact there could be only this:
.PHONY: terraform-custom
terraform-custom: terraform-init
	echo "===================================================================================="
	echo "Terraform custom command (${env})"
	echo "> terraform ${cmd}"
	echo
	terraform ${cmd}

