#######################################################
# Project info: 
locals {

  # Set APP_NAME variable on the repository
  # app_name =

  app_port = "8080"
  app_cpu  = "512"
  app_mem  = "1024"

  # ALB Healthcheck:
  healthcheck_protocol             = "HTTP"
  healthcheck_path                 = "/healthcheck"
  healthcheck_port                 = local.app_port
  healthcheck_matcher              = "200,301"
  healthcheck_interval             = "5"
  healthcheck_grace_period_seconds = "30"
  healthcheck_timeout              = "3"
  healthcheck_healthy_threshold    = "2"
  healthcheck_unhealthy_threshold  = "2"

  # Scaling parameters can be defined by environment:
  mem_target_value         = { dev = "70", stg = "70", prd = "70" }
  cpu_target_value         = { dev = "70", stg = "70", prd = "70" }
  requests_target_value    = { dev = "1000", stg = "1500", prd = "2000" }
  min_capacity_value       = { dev = "1", stg = "5", prd = "5" }
  desired_capacity_value   = { dev = "3", stg = "10", prd = "10" }
  max_capacity_value       = { dev = "10", stg = "30", prd = "30" }
  scale_in_cooldown_value  = { dev = "300", stg = "300", prd = "300" }
  scale_out_cooldown_value = { dev = "60", stg = "60", prd = "60" }

  # Cloudwatch log retention
  cloudwatch_log_retention_in_days_value = { dev = "5", stg = "5", prd = "30" }
  
 
}
