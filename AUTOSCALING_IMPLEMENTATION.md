# Implementación de Autoscaling Opcional para ECS Services

## Resumen de Cambios

Se ha implementado autoscaling **opcional** para los servicios ECS con tres políticas independientes: CPU, memoria y peticiones (requests). Actualmente solo está habilitado para `enterprise-webservice-api`.

## Archivos Modificados

### 1. `terraform/modules/api-service/variables.tf`
- ✅ Agregadas variables de configuración de autoscaling:
  - `enable_autoscaling` (bool, default: true)
  - `min_capacity` (number, default: 1)
  - `max_capacity` (number, default: 10)
  - `cpu_target_value` (number, default: 70)
  - `memory_target_value` (number, default: 70)
  - `requests_target_value` (number, default: 1000)
  - `scale_in_cooldown` (number, default: 300)
  - `scale_out_cooldown` (number, default: 60)

### 2. `terraform/modules/api-service/main.tf`
- ✅ Cambiado `desired_count = 1` (antes era 0)
- ✅ Agregado `aws_appautoscaling_target` para ECS service
- ✅ Agregado `aws_appautoscaling_policy` para CPU
- ✅ Agregado `aws_appautoscaling_policy` para memoria
- ✅ Agregado `aws_appautoscaling_policy` para peticiones (ALB requests)

### 3. `terraform/modules/api-service/outputs.tf`
- ✅ Agregados outputs para recursos de autoscaling:
  - `autoscaling_target_resource_id`
  - `cpu_policy_arn`
  - `memory_policy_arn`
  - `requests_policy_arn`

### 4. `terraform/variables.tf`
- ✅ Agregada variable `requests_target_value` por ambiente
- ✅ Actualizados cooldowns a valores recomendados (300s scale-in, 60s scale-out)

### 5. `terraform/enterprise_service.tf`
- ✅ Configurado el módulo para usar variables de autoscaling por ambiente
- ✅ Habilitado autoscaling para todos los servicios enterprise

### 6. `terraform/modules/api-service/README.md`
- ✅ Documentación completa del autoscaling
- ✅ Ejemplos de configuración
- ✅ Explicación del comportamiento de las políticas

## Archivos Creados

### 7. `terraform/examples/autoscaling-test.md`
- ✅ Guía completa para probar el autoscaling
- ✅ Comandos AWS CLI para verificar configuración
- ✅ Ejemplos de pruebas de carga
- ✅ Troubleshooting y diagnóstico

## Configuración por Ambiente

| Variable | dev | stg | prd |
|----------|-----|-----|-----|
| min_capacity | 1 | 5 | 5 |
| max_capacity | 10 | 30 | 30 |
| cpu_target_value | 70% | 70% | 70% |
| memory_target_value | 70% | 70% | 70% |
| requests_target_value | 1000 | 1500 | 2000 |
| scale_in_cooldown | 300s | 300s | 300s |
| scale_out_cooldown | 60s | 60s | 60s |

## Comportamiento del Autoscaling

### Políticas Implementadas

1. **CPU Policy**
   - Si CPU > 70% por más de 60s → Agregar instancia
   - Si CPU < 70% por más de 300s → Remover instancia

2. **Memory Policy**
   - Si Memoria > 70% por más de 60s → Agregar instancia
   - Si Memoria < 70% por más de 300s → Remover instancia

3. **Requests Policy**
   - Si Requests/target > threshold por más de 60s → Agregar instancia
   - Si Requests/target < threshold por más de 300s → Remover instancia

### Características Importantes

- ✅ `desired_count = 1` (no manejado por Terraform después del deploy inicial)
- ✅ `ignore_changes = [desired_count, task_definition]` configurado
- ✅ Las tres políticas funcionan independientemente
- ✅ AWS ECS toma la política que requiera más instancias
- ✅ Cooldowns previenen "flapping" (escalado excesivo)

## Servicios Afectados

**Configuración actual (autoscaling opcional por servicio):**
- ✅ enterprise-webservice-api (autoscaling habilitado)
- ⚪ enterprise-account-api (autoscaling deshabilitado)
- ⚪ enterprise-general-api (autoscaling deshabilitado)
- ⚪ enterprise-blacklist-api (autoscaling deshabilitado)
- ⚪ enterprise-report-api (autoscaling deshabilitado)
- ⚪ enterprise-maintenance-api (autoscaling deshabilitado)
- ⚪ common-shorturl-api (autoscaling deshabilitado)
- ⚪ common-mailer-api (autoscaling deshabilitado)

**Para habilitar autoscaling en otros servicios:**
Cambiar `enable_autoscaling = true` en la configuración del servicio en `enterprise_service.tf`

## Cómo Habilitar/Deshabilitar Autoscaling

### Habilitar autoscaling para un servicio

En `terraform/enterprise_service.tf`, cambiar la configuración del servicio:

```hcl
"enterprise-account-api" = {
  image            = "ubuntu"
  cpu              = "256"
  memory           = "512"
  ports            = 8080
  health_check_url = "/account-cl/health"
  enable_efs       = false
  enable_autoscaling = true  # Cambiar de false a true
},
```

### Deshabilitar autoscaling para un servicio

```hcl
"enterprise-webservice-api" = {
  image            = "ubuntu"
  cpu              = "256"
  memory           = "512"
  ports            = 8080
  health_check_url = "/ws-enterprise-cl/health"
  enable_efs       = false
  enable_autoscaling = false  # Cambiar de true a false
},
```

### Configuración por defecto

- Si no se especifica `enable_autoscaling`, el valor por defecto es `false`
- Los servicios sin autoscaling mantienen `min_capacity = max_capacity = 1`
- Los servicios con autoscaling usan los valores configurados por ambiente

## Próximos Pasos

1. **Deploy**: Ejecutar `terraform plan` y `terraform apply`
2. **Verificación**: Usar comandos en `terraform/examples/autoscaling-test.md`
3. **Monitoreo**: Configurar alertas de CloudWatch para eventos de autoscaling
4. **Pruebas**: Ejecutar pruebas de carga para validar el comportamiento

## Comandos de Verificación Rápida

```bash
# Verificar configuración de autoscaling
aws application-autoscaling describe-scalable-targets --service-namespace ecs

# Ver políticas de autoscaling
aws application-autoscaling describe-scaling-policies --service-namespace ecs

# Monitorear número de tareas
aws ecs describe-services --cluster your-cluster --services enterprise-account-api
```

## Notas Técnicas

- El autoscaling usa métricas de CloudWatch con período de evaluación de 1 minuto
- Las métricas de ALB (requests) están disponibles inmediatamente
- Las métricas de ECS (CPU/memoria) pueden tardar hasta 5 minutos
- El scale-in es más conservador que el scale-out para evitar interrupciones
