# Fix: Error "Tag values cannot be empty or blank" en Autoscaling

## Problema Original

```
Error: creating Application AutoScaling Target (service/smscorpchile-ecs-cluster-dev/enterprise-webservice-api): 
operation error Application Auto Scaling: RegisterScalableTarget, 
https response error StatusCode: 400, RequestID: 980db899-7d87-470d-ba68-de348d8b68d8, 
ValidationException: Tag values cannot be empty or blank
```

## Causa del Problema

El error ocurre porque:

1. El provider AWS está configurado con `default_tags` en `provider-aws.tf`
2. Algunos de estos tags pueden tener valores vacíos o nulos
3. El recurso `aws_appautoscaling_target` hereda estos tags automáticamente
4. AWS Application Auto Scaling no permite tags con valores vacíos

## Solución Implementada

### 1. Tags Explícitos en el Recurso

Se agregaron tags explícitos al recurso `aws_appautoscaling_target`:

```hcl
resource "aws_appautoscaling_target" "ecs_target" {
  count              = var.enable_autoscaling ? 1 : 0
  service_namespace  = "ecs"
  resource_id        = "service/${var.cluster_name}/${aws_ecs_service.svc.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  min_capacity       = var.min_capacity
  max_capacity       = var.max_capacity

  tags = merge({
    Name      = "${var.name}-autoscaling-target"
    Service   = var.name
    terraform = "true"
  }, var.tags)

  depends_on = [aws_ecs_service.svc]
}
```

### 2. Variable de Tags en el Módulo

Se agregó una nueva variable al módulo `api-service`:

```hcl
variable "tags" {
  type        = map(string)
  default     = {}
  description = "Additional tags to apply to autoscaling resources"
}
```

### 3. Tags Pasados desde enterprise_service.tf

Se configuraron tags específicos en la llamada al módulo:

```hcl
module "java_api_services" {
  for_each = local.enterprise_apis
  source   = "./modules/api-service"
  
  # ... otras configuraciones ...
  
  # Tags para evitar problemas con tags vacíos
  tags = {
    Environment = local.env
    Service     = each.key
  }
}
```

## Archivos Modificados

### 1. `terraform/modules/api-service/variables.tf`
- ✅ Agregada variable `tags` para tags adicionales

### 2. `terraform/modules/api-service/main.tf`
- ✅ Modificado `aws_appautoscaling_target` para usar tags explícitos
- ✅ Uso de `merge()` para combinar tags por defecto con tags pasados

### 3. `terraform/enterprise_service.tf`
- ✅ Agregados tags específicos en la llamada al módulo

## Cómo Funciona la Solución

1. **Tags Garantizados**: Los tags básicos (`Name`, `Service`, `terraform`) siempre tienen valores
2. **Tags Adicionales**: Se pueden pasar tags adicionales desde el módulo padre
3. **Merge Seguro**: La función `merge()` combina ambos conjuntos de tags
4. **Sin Herencia Problemática**: Los tags explícitos evitan problemas con `default_tags` del provider

## Verificación

Para verificar que la solución funciona:

```bash
# Validar configuración
terraform validate

# Plan específico para el autoscaling target
terraform plan -target=module.java_api_services[\"enterprise-webservice-api\"].aws_appautoscaling_target.ecs_target[0]

# Aplicar solo el autoscaling target
terraform apply -target=module.java_api_services[\"enterprise-webservice-api\"].aws_appautoscaling_target.ecs_target[0]
```

## Prevención Futura

Para evitar este problema en el futuro:

1. **Siempre usar tags explícitos** en recursos sensibles como Application Auto Scaling
2. **Validar que los tags del provider no tengan valores vacíos**
3. **Usar `merge()` para combinar tags de manera segura**
4. **Probar con `terraform plan` antes de aplicar cambios**

## Notas Importantes

- Esta solución es compatible con la configuración existente
- No afecta otros recursos que ya funcionan correctamente
- Los tags explícitos tienen precedencia sobre los `default_tags` del provider
- La solución es escalable para futuros servicios con autoscaling
