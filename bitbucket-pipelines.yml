image: hashicorp/terraform:1.7.3
options:
  size: "2x"

definitions:
  steps:
    ##########################################################################
    # Dev environment:
    - step: &plan_dev
        name: "[dev] Terraform plan"
        oidc: true
        artifacts:
          paths:
            - terraform/plan*
            - .terraform/*
            - .terraform.lock.hcl
        script:
          - ./run-terraform.sh plan dev terraform

    - step: &apply_dev
        name: "[dev] Terraform apply"
        deployment: "Development"
        oidc: true
        script:
          - ./run-terraform.sh apply dev terraform

    - step: &destroy_dev
        name: "[dev] Terraform destroy"
        oidc: true
        script:
          - ./run-terraform.sh destroy dev terraform

    ##########################################################################
    # Stg environment:
    - step: &plan_stg
        name: "[stg] Terraform plan"
        oidc: true
        artifacts:
          paths:
            - terraform/plan*
            - .terraform/*
            - .terraform.lock.hcl
        script:
          - ./run-terraform.sh plan stg terraform

    - step: &apply_stg
        name: "[stg] Terraform apply"
        deployment: "Staging"
        oidc: true
        script:
          - ./run-terraform.sh apply stg terraform

    - step: &destroy_stg
        name: "[stg] Terraform destroy"
        oidc: true
        script:
          - ./run-terraform.sh destroy stg terraform

    ##########################################################################
    # Prd environment:
    - step: &plan_prd
        name: "[prd] Terraform plan"
        oidc: true
        artifacts:
          paths:
            - terraform/plan*
            - .terraform/*
            - .terraform.lock.hcl
        script:
          - ./run-terraform.sh plan prd terraform

    - step: &apply_prd
        name: "[prd] Terraform apply"
        deployment: "Production"
        oidc: true
        script:
          - ./run-terraform.sh apply prd terraform

    - step: &destroy_prd
        name: "[prd] Terraform destroy"
        oidc: true
        script:
          - ./run-terraform.sh destroy prd terraform
##############################################################################
pipelines:
  default:
    - parallel:
        - step: *plan_dev
        - step: *plan_prd
    #     - step: *trivy_iac
    #   # - step: *infracost
    - step:
        <<: *apply_dev
        trigger: manual

  custom:
    plan_dev:
      - step: *plan_dev
    apply_dev:
      - step: *apply_dev
    destroy_dev:
      - step: *destroy_dev

    plan_stg:
      - step: *plan_stg
    apply_stg:
      - step: *apply_stg
    destroy_stg:
      - step: *destroy_stg
    plan_prd:
      - step: *plan_prd
    apply_prd:
      - step: *apply_prd
