#! /bin/sh
#################################################
# This is just a wrapper for running terraform  #
# operations to avoid many terraform commands   #
# on pipeline script.                           #
# But it should be kept generic and usable by   #
# every project. It must not contain project    #
# specific logic.                               #
#################################################

# Args:
export cmd=$1
export env=$2 || export env="default"
export dir=$3 || export dir="terraform"
export arg=$4

echo "===================================================================================="
echo "Running wrapper with command:"
echo "$0 $@"
echo "Parsed cmd=$cmd env=$env dir=$dir"
echo

echo "===================================================================================="
echo -n "Installing Make command... "
apk add make >> /dev/null 2>> /dev/null
echo "OK"

echo -n "Installing AWS CLI command... "
apk add aws-cli >> /dev/null 2>> /dev/null
echo "OK"
echo

echo "===================================================================================="
# Read this repo specific vars and OpenID vars if they exist:
for VARFILE in vars*.sh; do
  test -x $VARFILE && source $VARFILE && echo "Loaded $VARFILE"
done
echo

echo "Variables:"
echo "- AWS_ROLE_ARN: ${AWS_ROLE_ARN}"
echo "- AWS_WEB_IDENTITY_TOKEN_FILE: ${AWS_WEB_IDENTITY_TOKEN_FILE}"
echo "- TF_VAR_repository: ${TF_VAR_repository}"
echo "- TF_VAR_commit: ${TF_VAR_commit}"
echo "- TF_VAR_aws_main_account_id: ${TF_VAR_aws_main_account_id}"
echo

echo "===================================================================================="
echo -n "Calling make on $dir: "
cd $dir || exit 1 
echo
case $cmd in
  init|plan|apply|destroy|plan-destroy|state-list) 
    echo "make terraform-$cmd env=$env" ;
    make terraform-$cmd env=$env ; exit $? ;;
  output) 
    echo "make terraform-$cmd env=$env arg=$arg" ;
    make terraform-$cmd env=$env arg=$arg ; exit $? ;;
  *) 
    echo "make terraform-custom cmd=$cmd env=$env" ;
    make terraform-custom cmd="$cmd" env=$env ; exit $? ;;
esac
echo

